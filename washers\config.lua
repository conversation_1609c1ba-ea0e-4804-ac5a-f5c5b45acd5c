config = {}

--- Periodic save timer
config.periodic_save_timer = 1 -- Time in minutes to save washers periodically, they will save on resource stop also.

--- Admin ranks.
--- Any ranks here are allowed to place and modify existing washers.
--- These are `boii_utils` admin ranks; default ranks: ('member', 'mod', 'admin', 'dev', 'owner').
--- Remove 'member' if you do not want everyone to be able to use the commands <--- highly recommended
config.admin_ranks = { 'member', 'mod', 'admin', 'dev', 'owner' }

--- Default wash settings.
config.defaults = {
    take_value = 2000, -- Amount to add by increment. 
    return_value = 60, -- Return value for new washers 60%
    max_capacity = 10000, -- Max amount that can be washed per interaction $10000
    max_limit = 500000, -- Limit per interval that can be washed default = 500k in 24hours
    limit_interval = 24, -- Amount of hours to pass to reset limit.
    wash_time = 120, -- Time to wash money in seconds (s).
}

--- Actions for washers handled by DUI.
config.actions = {
    { 
        key = 'G', -- Interaction key.
        label = 'Add Money', -- Display label for key interaction.
        action_type = 'server',
        action = 'boii_moneywash:sv:add_money'
    },
    { 
        key = 'H', -- Interaction key.
        label = 'Remove Money', -- Display label for key interaction.
        action_type = 'server',
        action = 'boii_moneywash:sv:remove_money'
    },
    { 
        key = 'F', -- Interaction key.
        label = 'Start Washing', -- Display label for key interaction.
        action_type = 'server',
        action = 'boii_moneywash:sv:start_washing'
    }
}

--- Prop settings.
config.prop = {
    model = 'bkr_prop_prtmachine_dryer_spin', -- Prop model to spawn for washers.
    header = 'Money Wash', -- DUI header.
    icon = 'fa-solid fa-user-ninja', -- DUI header icon.
    outline = true
}

--- Police settings.
config.police = {
    alert_chance = 100, -- Chance to alert police when washing.
    required_to_wash = 2, -- Amount of online police jobs required to wash, set to 0 to allow with no cops.
    disable_duration = 1, -- Amount of time in minutes police jobs can disable a washer for.
    disable_range = 10, -- Distance police players can disable a nearby washer.
    on_duty_only = true, -- Only counts on duty jobs if true.
    jobs = { 'police', 'fib' } -- Job names classed as "police".
}

--- Washer take settings.
config.takes = {
    type = 'money', -- Options: 'money' or 'item'
    source = { -- Source to take from.
        id = 'cash', -- Account id or item id to take money from
        metadata = { -- Metadata if required, make sure to include the `.` seperator e.g, qb markedbills use `info.worth` 
           -- 'info.worth' uncomment if using `markedbills`
        }
    }
}

--- Washer return settings.
config.returns = {
    type = 'money', -- Options: 'money' or 'item'
    source = {
        id = 'bank', -- Account id or item id to return money to
        metadata = {} -- Metadata if required e.g, qb marked bills
    }
}